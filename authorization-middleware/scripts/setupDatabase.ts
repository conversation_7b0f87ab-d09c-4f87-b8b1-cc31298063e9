import { Pool } from 'pg';
import * as dotenv from 'dotenv';

dotenv.config();

async function setupDatabase() {
  console.log('🚀 Setting up database for Authorization Middleware Demo\n');

  const pool = new Pool({
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: process.env.DB_NAME || 'ab_testing_auth',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password'
  });

  try {
    // Test connection
    await pool.query('SELECT NOW()');
    console.log('✅ Database connection successful');

    // Create tables
    console.log('📋 Creating database tables...');

    // Users table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        first_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
        last_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
        role VARCHAR(50) NOT NULL DEFAULT 'viewer',
        tenant_id VARCHAR(255) NOT NULL,
        is_active BOOLEAN DEFAULT true,
        email_verified BOOLEAN DEFAULT false,
        last_login_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Users table created');

    // Experiments table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS experiments (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        description TEXT,
        type VARCHAR(100) NOT NULL DEFAULT 'ab_test',
        status VARCHAR(50) NOT NULL DEFAULT 'draft',
        tenant_id VARCHAR(255) NOT NULL,
        created_by VARCHAR(255) NOT NULL,
        updated_by VARCHAR(255),
        published_at TIMESTAMP WITH TIME ZONE,
        archived_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        config JSONB DEFAULT '{}',
        metadata JSONB DEFAULT '{}'
      )
    `);
    console.log('✅ Experiments table created');

    // Authorization audit log table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS authorization_audit_log (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id VARCHAR(255) NOT NULL,
        tenant_id VARCHAR(255) NOT NULL,
        resource VARCHAR(255) NOT NULL,
        action VARCHAR(255) NOT NULL,
        granted BOOLEAN NOT NULL,
        reason TEXT NOT NULL,
        ip_address INET,
        user_agent TEXT,
        request_id VARCHAR(255),
        duration INTEGER DEFAULT 0,
        experiment_id VARCHAR(255),
        resource_id VARCHAR(255),
        details JSONB DEFAULT '{}',
        timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Authorization audit log table created');

    // Security events table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS security_events (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id VARCHAR(255),
        tenant_id VARCHAR(255),
        event_type VARCHAR(100) NOT NULL,
        severity VARCHAR(20) NOT NULL,
        description TEXT NOT NULL,
        ip_address INET,
        user_agent TEXT,
        request_id VARCHAR(255),
        metadata JSONB DEFAULT '{}',
        timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Security events table created');

    // Roles table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS roles (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) UNIQUE NOT NULL,
        display_name VARCHAR(255) NOT NULL,
        description TEXT,
        type VARCHAR(50) NOT NULL DEFAULT 'system',
        is_system BOOLEAN DEFAULT false,
        tenant_id VARCHAR(255),
        permissions JSONB DEFAULT '[]',
        inherits_from JSONB DEFAULT '[]',
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        created_by VARCHAR(255)
      )
    `);
    console.log('✅ Roles table created');

    // Permissions table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS permissions (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) UNIQUE NOT NULL,
        description TEXT NOT NULL,
        resource VARCHAR(255) NOT NULL,
        action VARCHAR(255) NOT NULL,
        conditions JSONB DEFAULT '[]',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Permissions table created');

    // User roles table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS user_roles (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id VARCHAR(255) NOT NULL,
        role_id UUID NOT NULL REFERENCES roles(id),
        tenant_id VARCHAR(255) NOT NULL,
        assigned_by VARCHAR(255) NOT NULL,
        assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP WITH TIME ZONE,
        is_active BOOLEAN DEFAULT true,
        conditions JSONB DEFAULT '[]'
      )
    `);
    console.log('✅ User roles table created');

    // Create indexes for performance
    console.log('📊 Creating database indexes...');

    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id);
    `);

    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_experiments_tenant_id ON experiments(tenant_id);
    `);

    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_experiments_created_by ON experiments(created_by);
    `);

    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_auth_audit_user_tenant ON authorization_audit_log(user_id, tenant_id);
    `);

    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_auth_audit_timestamp ON authorization_audit_log(timestamp);
    `);

    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_auth_audit_resource_action ON authorization_audit_log(resource, action);
    `);

    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_security_events_tenant ON security_events(tenant_id);
    `);

    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_security_events_timestamp ON security_events(timestamp);
    `);

    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_user_roles_user_tenant ON user_roles(user_id, tenant_id);
    `);

    console.log('✅ Database indexes created');

    console.log('\n🎉 Database setup completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('   1. Copy .env.example to .env and update values');
    console.log('   2. Run: npm run seed-data');
    console.log('   3. Run: npm run demo');

  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

if (require.main === module) {
  setupDatabase();
}

export { setupDatabase };
