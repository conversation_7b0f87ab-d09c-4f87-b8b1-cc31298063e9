import { Pool } from 'pg';
import * as bcrypt from 'bcrypt';
import * as dotenv from 'dotenv';

dotenv.config();

async function seedData() {
  console.log('🌱 Seeding demo data for Authorization Middleware\n');

  const pool = new Pool({
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: process.env.DB_NAME || 'ab_testing_auth',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password'
  });

  try {
    // Clear existing data
    console.log('🧹 Clearing existing data...');
    await pool.query('DELETE FROM user_roles');
    await pool.query('DELETE FROM authorization_audit_log');
    await pool.query('DELETE FROM security_events');
    await pool.query('DELETE FROM experiments');
    await pool.query('DELETE FROM users');
    await pool.query('DELETE FROM roles');
    await pool.query('DELETE FROM permissions');

    // Seed permissions
    console.log('🔑 Creating permissions...');
    const permissions = [
      { name: 'experiments:read', description: 'View experiments', resource: 'experiments', action: 'read' },
      { name: 'experiments:create', description: 'Create experiments', resource: 'experiments', action: 'create' },
      { name: 'experiments:update', description: 'Update experiments', resource: 'experiments', action: 'update' },
      { name: 'experiments:delete', description: 'Delete experiments', resource: 'experiments', action: 'delete' },
      { name: 'experiments:publish', description: 'Publish experiments', resource: 'experiments', action: 'publish' },
      { name: 'experiments:archive', description: 'Archive experiments', resource: 'experiments', action: 'archive' },
      { name: 'analytics:view', description: 'View analytics', resource: 'analytics', action: 'view' },
      { name: 'analytics:export', description: 'Export analytics', resource: 'analytics', action: 'export' },
      { name: 'users:read', description: 'View users', resource: 'users', action: 'read' },
      { name: 'users:create', description: 'Create users', resource: 'users', action: 'create' },
      { name: 'users:update', description: 'Update users', resource: 'users', action: 'update' },
      { name: 'users:delete', description: 'Delete users', resource: 'users', action: 'delete' },
      { name: 'users:invite', description: 'Invite users', resource: 'users', action: 'invite' },
      { name: 'settings:read', description: 'View settings', resource: 'settings', action: 'read' },
      { name: 'settings:update', description: 'Update settings', resource: 'settings', action: 'update' },
      { name: 'roles:assign', description: 'Assign roles', resource: 'roles', action: 'assign' },
      { name: 'roles:unassign', description: 'Unassign roles', resource: 'roles', action: 'unassign' }
    ];

    for (const permission of permissions) {
      await pool.query(`
        INSERT INTO permissions (name, description, resource, action)
        VALUES ($1, $2, $3, $4)
      `, [permission.name, permission.description, permission.resource, permission.action]);
    }
    console.log(`✅ Created ${permissions.length} permissions`);

    // Seed roles
    console.log('👥 Creating roles...');
    const roles = [
      {
        name: 'viewer',
        display_name: 'Viewer',
        description: 'Read-only access to experiments and analytics',
        permissions: ['experiments:read', 'analytics:view']
      },
      {
        name: 'experimenter',
        display_name: 'Experimenter',
        description: 'Can create and manage experiments',
        permissions: [
          'experiments:read', 'experiments:create', 'experiments:update', 'experiments:publish',
          'analytics:view', 'analytics:export'
        ]
      },
      {
        name: 'admin',
        display_name: 'Admin',
        description: 'Full access to experiments, users, and settings',
        permissions: [
          'experiments:read', 'experiments:create', 'experiments:update', 'experiments:delete',
          'experiments:publish', 'experiments:archive',
          'analytics:view', 'analytics:export',
          'users:read', 'users:create', 'users:update', 'users:delete', 'users:invite',
          'settings:read', 'settings:update',
          'roles:assign', 'roles:unassign'
        ]
      }
    ];

    for (const role of roles) {
      await pool.query(`
        INSERT INTO roles (name, display_name, description, permissions, is_system)
        VALUES ($1, $2, $3, $4, true)
      `, [role.name, role.display_name, role.description, JSON.stringify(role.permissions)]);
    }
    console.log(`✅ Created ${roles.length} roles`);

    // Seed users
    console.log('👤 Creating demo users...');
    const hashedPassword = await bcrypt.hash('demo123', 10);
    
    const users = [
      {
        id: 'admin_123',
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin',
        tenantId: 'tenant_123'
      },
      {
        id: 'experimenter_456',
        email: '<EMAIL>',
        firstName: 'Experimenter',
        lastName: 'User',
        role: 'experimenter',
        tenantId: 'tenant_123'
      },
      {
        id: 'viewer_789',
        email: '<EMAIL>',
        firstName: 'Viewer',
        lastName: 'User',
        role: 'viewer',
        tenantId: 'tenant_123'
      },
      {
        id: 'user_999',
        email: '<EMAIL>',
        firstName: 'Other',
        lastName: 'User',
        role: 'experimenter',
        tenantId: 'tenant_999'
      }
    ];

    for (const user of users) {
      await pool.query(`
        INSERT INTO users (id, email, password_hash, first_name, last_name, role, tenant_id, email_verified)
        VALUES ($1, $2, $3, $4, $5, $6, $7, true)
      `, [user.id, user.email, hashedPassword, user.firstName, user.lastName, user.role, user.tenantId]);
    }
    console.log(`✅ Created ${users.length} demo users`);

    // Assign roles to users
    console.log('🔗 Assigning roles to users...');
    const roleAssignments = [
      { userId: 'admin_123', roleName: 'admin', tenantId: 'tenant_123' },
      { userId: 'experimenter_456', roleName: 'experimenter', tenantId: 'tenant_123' },
      { userId: 'viewer_789', roleName: 'viewer', tenantId: 'tenant_123' },
      { userId: 'user_999', roleName: 'experimenter', tenantId: 'tenant_999' }
    ];

    for (const assignment of roleAssignments) {
      const roleResult = await pool.query('SELECT id FROM roles WHERE name = $1', [assignment.roleName]);
      if (roleResult.rows.length > 0) {
        await pool.query(`
          INSERT INTO user_roles (user_id, role_id, tenant_id, assigned_by)
          VALUES ($1, $2, $3, 'system')
        `, [assignment.userId, roleResult.rows[0].id, assignment.tenantId]);
      }
    }
    console.log(`✅ Assigned roles to ${roleAssignments.length} users`);

    // Seed experiments
    console.log('🧪 Creating demo experiments...');
    const experiments = [
      {
        id: 'exp_001',
        name: 'Button Color Test',
        description: 'Testing different button colors for conversion',
        type: 'ab_test',
        status: 'active',
        tenantId: 'tenant_123',
        createdBy: 'experimenter_456'
      },
      {
        id: 'exp_002',
        name: 'Pricing Page Layout',
        description: 'Testing different pricing page layouts',
        type: 'multivariate',
        status: 'draft',
        tenantId: 'tenant_123',
        createdBy: 'admin_123'
      },
      {
        id: 'exp_003',
        name: 'Checkout Flow Optimization',
        description: 'Optimizing the checkout flow for better conversion',
        type: 'ab_test',
        status: 'paused',
        tenantId: 'tenant_123',
        createdBy: 'experimenter_456'
      },
      {
        id: 'exp_004',
        name: 'Other Tenant Experiment',
        description: 'Experiment from different tenant',
        type: 'ab_test',
        status: 'active',
        tenantId: 'tenant_999',
        createdBy: 'user_999'
      }
    ];

    for (const experiment of experiments) {
      await pool.query(`
        INSERT INTO experiments (id, name, description, type, status, tenant_id, created_by)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
      `, [experiment.id, experiment.name, experiment.description, experiment.type, experiment.status, experiment.tenantId, experiment.createdBy]);
    }
    console.log(`✅ Created ${experiments.length} demo experiments`);

    console.log('\n🎉 Demo data seeded successfully!');
    console.log('\n👤 Demo Users (password: demo123):');
    console.log('   <EMAIL>      - Admin (tenant_123)');
    console.log('   <EMAIL> - Experimenter (tenant_123)');
    console.log('   <EMAIL>     - Viewer (tenant_123)');
    console.log('   <EMAIL>         - Experimenter (tenant_999)');
    
    console.log('\n🔑 Demo Tokens for API testing:');
    console.log('   admin_token           - <NAME_EMAIL>');
    console.log('   experimenter_token    - <NAME_EMAIL>');
    console.log('   viewer_token          - <NAME_EMAIL>');
    console.log('   other_tenant_token    - <NAME_EMAIL>');

    console.log('\n🧪 Demo Experiments:');
    console.log('   exp_001 - Button Color Test (experimenter_456)');
    console.log('   exp_002 - Pricing Page Layout (admin_123)');
    console.log('   exp_003 - Checkout Flow Optimization (experimenter_456)');
    console.log('   exp_004 - Other Tenant Experiment (user_999)');

  } catch (error) {
    console.error('❌ Data seeding failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

if (require.main === module) {
  seedData();
}

export { seedData };
