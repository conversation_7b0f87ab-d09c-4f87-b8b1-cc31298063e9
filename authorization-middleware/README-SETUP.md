# Authorization Middleware - Local Setup Guide

This guide will help you run the Authorization Middleware demo on your local machine.

## Prerequisites

- **Node.js** (v18 or higher)
- **PostgreSQL** (v12 or higher)
- **Redis** (optional, for production features)

## Quick Start

### 1. Install Dependencies

```bash
cd authorization-middleware
npm install
```

### 2. Setup Environment

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your database credentials
# Default values should work for local PostgreSQL installation
```

### 3. Setup Database

```bash
# Create database (if it doesn't exist)
createdb ab_testing_auth

# Setup database tables
npm run setup-db

# Seed demo data
npm run seed-data
```

### 4. Start the Demo Server

```bash
# Start the authorization demo server
npm run dev
```

The server will start on `http://localhost:3003`

## Testing the Authorization System

### Available Test Tokens

Use these tokens in the `Authorization: Bearer <token>` header:

- **`admin_token`** - Admin user with full access
- **`experimenter_token`** - Experimenter with experiment management permissions
- **`viewer_token`** - Read-only viewer access
- **`other_tenant_token`** - User from different tenant (cross-tenant tests)

### Test Endpoints

#### 1. Health Check
```bash
curl http://localhost:3003/health
```

#### 2. List Experiments (Success - All Users)
```bash
curl -H "Authorization: Bearer admin_token" \
     http://localhost:3003/api/experiments

curl -H "Authorization: Bearer experimenter_token" \
     http://localhost:3003/api/experiments

curl -H "Authorization: Bearer viewer_token" \
     http://localhost:3003/api/experiments
```

#### 3. Create Experiment (Success - Admin/Experimenter)
```bash
curl -X POST \
     -H "Authorization: Bearer experimenter_token" \
     -H "Content-Type: application/json" \
     -d '{"name":"Test Experiment","description":"Testing authorization"}' \
     http://localhost:3003/api/experiments
```

#### 4. User Management (Success - Admin Only)
```bash
curl -H "Authorization: Bearer admin_token" \
     http://localhost:3003/api/users
```

#### 5. User Management (Denied - Non-Admin)
```bash
curl -H "Authorization: Bearer viewer_token" \
     http://localhost:3003/api/users
```

#### 6. Cross-Tenant Access (Denied)
```bash
curl -H "Authorization: Bearer other_tenant_token" \
     http://localhost:3003/api/experiments
```

#### 7. Analytics Export (Success - Admin/Experimenter)
```bash
curl -H "Authorization: Bearer experimenter_token" \
     http://localhost:3003/api/analytics/export
```

#### 8. Analytics Export (Denied - Viewer)
```bash
curl -H "Authorization: Bearer viewer_token" \
     http://localhost:3003/api/analytics/export
```

#### 9. Get Specific Experiment (Ownership Check)
```bash
# Success - Owner or Admin
curl -H "Authorization: Bearer experimenter_token" \
     http://localhost:3003/api/experiments/exp_001

# Success - Admin can access any experiment
curl -H "Authorization: Bearer admin_token" \
     http://localhost:3003/api/experiments/exp_001

# Denied - Different owner
curl -H "Authorization: Bearer viewer_token" \
     http://localhost:3003/api/experiments/exp_001
```

#### 10. Multiple Permissions (Publish Experiment)
```bash
# Success - Has both update and publish permissions
curl -X POST \
     -H "Authorization: Bearer experimenter_token" \
     http://localhost:3003/api/experiments/exp_001/publish

# Denied - Viewer doesn't have publish permission
curl -X POST \
     -H "Authorization: Bearer viewer_token" \
     http://localhost:3003/api/experiments/exp_001/publish
```

### Expected Responses

#### Success Response
```json
{
  "success": true,
  "data": {
    "experiments": [
      {
        "id": "exp_001",
        "name": "Button Color Test",
        "status": "active",
        "tenantId": "tenant_123",
        "createdBy": "experimenter_456"
      }
    ]
  }
}
```

#### Authorization Denied Response
```json
{
  "success": false,
  "error": "Missing permission: users:read",
  "code": "INSUFFICIENT_PERMISSIONS",
  "requestId": "req_1703123456789_abc123",
  "timestamp": "2023-12-21T10:30:00.000Z",
  "details": {
    "requiredPermission": "users:read",
    "denyReasons": ["User does not have permission: users:read"]
  }
}
```

#### Ownership Violation Response
```json
{
  "success": false,
  "error": "User does not own this experiment",
  "code": "NOT_EXPERIMENT_OWNER",
  "requestId": "req_1703123456789_abc123",
  "timestamp": "2023-12-21T10:30:00.000Z",
  "details": {
    "experimentId": "exp_001",
    "experimentOwner": "experimenter_456",
    "requestingUser": "viewer_789"
  }
}
```

## Demo Data

### Users
- **<EMAIL>** (admin_123) - Admin role in tenant_123
- **<EMAIL>** (experimenter_456) - Experimenter role in tenant_123
- **<EMAIL>** (viewer_789) - Viewer role in tenant_123
- **<EMAIL>** (user_999) - Experimenter role in tenant_999

### Experiments
- **exp_001** - Button Color Test (owned by experimenter_456)
- **exp_002** - Pricing Page Layout (owned by admin_123)
- **exp_003** - Checkout Flow Optimization (owned by experimenter_456)
- **exp_004** - Other Tenant Experiment (owned by user_999, different tenant)

### Roles & Permissions

#### Viewer Role
- `experiments:read`
- `analytics:view`

#### Experimenter Role
- `experiments:read`, `experiments:create`, `experiments:update`, `experiments:publish`
- `analytics:view`, `analytics:export`

#### Admin Role
- All experimenter permissions plus:
- `experiments:delete`, `experiments:archive`
- `users:read`, `users:create`, `users:update`, `users:delete`, `users:invite`
- `settings:read`, `settings:update`
- `roles:assign`, `roles:unassign`

## Monitoring Authorization

### View Authorization Statistics
```bash
curl -H "Authorization: Bearer admin_token" \
     "http://localhost:3003/api/admin/authorization-stats?timeframe=day"
```

### View Security Events
```bash
curl -H "Authorization: Bearer admin_token" \
     "http://localhost:3003/api/admin/security-events?limit=20"
```

## Troubleshooting

### Database Connection Issues
```bash
# Check if PostgreSQL is running
pg_isready

# Check if database exists
psql -l | grep ab_testing_auth

# Recreate database if needed
dropdb ab_testing_auth
createdb ab_testing_auth
npm run setup-db
npm run seed-data
```

### Permission Issues
- Make sure you're using the correct token for the operation
- Check that the user has the required role/permissions
- Verify the experiment ownership for ownership-based operations

### Logs
The server logs all authorization attempts with detailed information:
- Permission checks (granted/denied)
- Ownership verification
- Security events
- Error details

## Development

### Build
```bash
npm run build
```

### Run Tests
```bash
npm test
```

### Development Mode
```bash
npm run dev
```

## Architecture

The authorization system consists of:

1. **AuthorizationMiddleware** - Main middleware for permission checking
2. **MockRBACService** - Role-based access control service (mock implementation)
3. **MockExperimentService** - Experiment service for ownership verification (mock)
4. **AuditLogger** - Comprehensive audit logging
5. **Security Monitoring** - Automatic threat detection

## Next Steps

- Integrate with your actual RBAC and experiment services
- Add more complex permission conditions
- Implement rate limiting
- Add more comprehensive security monitoring
- Deploy to production environment

## Support

If you encounter any issues:
1. Check the server logs for detailed error information
2. Verify your database connection and data
3. Ensure you're using the correct authorization tokens
4. Review the API endpoint documentation above
