import { Logger } from '../utils/Logger';

export interface Experiment {
  id: string;
  name: string;
  description: string;
  type: string;
  status: string;
  tenantId: string;
  createdBy: string;
  updatedBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

export class MockExperimentService {
  private logger: Logger;
  
  // Mock experiments data
  private experiments: Experiment[] = [
    {
      id: 'exp_001',
      name: 'Button Color Test',
      description: 'Testing different button colors for conversion',
      type: 'ab_test',
      status: 'active',
      tenantId: 'tenant_123',
      createdBy: 'experimenter_456',
      createdAt: new Date('2023-12-01'),
      updatedAt: new Date('2023-12-01')
    },
    {
      id: 'exp_002',
      name: 'Pricing Page Layout',
      description: 'Testing different pricing page layouts',
      type: 'multivariate',
      status: 'draft',
      tenantId: 'tenant_123',
      createdBy: 'admin_123',
      createdAt: new Date('2023-12-02'),
      updatedAt: new Date('2023-12-02')
    },
    {
      id: 'exp_003',
      name: 'Checkout Flow Optimization',
      description: 'Optimizing the checkout flow for better conversion',
      type: 'ab_test',
      status: 'paused',
      tenantId: 'tenant_123',
      createdBy: 'experimenter_456',
      createdAt: new Date('2023-12-03'),
      updatedAt: new Date('2023-12-03')
    },
    {
      id: 'exp_004',
      name: 'Other Tenant Experiment',
      description: 'Experiment from different tenant',
      type: 'ab_test',
      status: 'active',
      tenantId: 'tenant_999',
      createdBy: 'user_999',
      createdAt: new Date('2023-12-04'),
      updatedAt: new Date('2023-12-04')
    }
  ];

  constructor(logger: Logger) {
    this.logger = logger;
  }

  async getExperiment(experimentId: string, tenantId: string): Promise<Experiment | null> {
    try {
      const experiment = this.experiments.find(exp => 
        exp.id === experimentId && exp.tenantId === tenantId
      );
      
      if (experiment) {
        this.logger.debug('Experiment found', { experimentId, tenantId });
        return experiment;
      } else {
        this.logger.debug('Experiment not found', { experimentId, tenantId });
        return null;
      }
    } catch (error) {
      this.logger.error('Failed to get experiment', { error, experimentId, tenantId });
      throw error;
    }
  }

  async listExperiments(options: {
    tenantId: string;
    userId?: string;
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
  }): Promise<{
    data: Experiment[];
    total: number;
    page: number;
    limit: number;
  }> {
    try {
      let filteredExperiments = this.experiments.filter(exp => 
        exp.tenantId === options.tenantId
      );

      // Apply status filter
      if (options.status) {
        filteredExperiments = filteredExperiments.filter(exp => 
          exp.status === options.status
        );
      }

      // Apply search filter
      if (options.search) {
        const searchLower = options.search.toLowerCase();
        filteredExperiments = filteredExperiments.filter(exp => 
          exp.name.toLowerCase().includes(searchLower) ||
          exp.description.toLowerCase().includes(searchLower)
        );
      }

      const total = filteredExperiments.length;
      const page = options.page || 1;
      const limit = options.limit || 20;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;

      const data = filteredExperiments.slice(startIndex, endIndex);

      this.logger.debug('Listed experiments', {
        tenantId: options.tenantId,
        total,
        page,
        limit,
        returned: data.length
      });

      return { data, total, page, limit };
    } catch (error) {
      this.logger.error('Failed to list experiments', { error, options });
      throw error;
    }
  }

  async createExperiment(experimentData: Partial<Experiment>): Promise<Experiment> {
    try {
      const newExperiment: Experiment = {
        id: `exp_${Date.now()}`,
        name: experimentData.name || 'New Experiment',
        description: experimentData.description || '',
        type: experimentData.type || 'ab_test',
        status: 'draft',
        tenantId: experimentData.tenantId!,
        createdBy: experimentData.createdBy!,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      this.experiments.push(newExperiment);

      this.logger.info('Experiment created', {
        experimentId: newExperiment.id,
        name: newExperiment.name,
        tenantId: newExperiment.tenantId,
        createdBy: newExperiment.createdBy
      });

      return newExperiment;
    } catch (error) {
      this.logger.error('Failed to create experiment', { error, experimentData });
      throw error;
    }
  }

  async updateExperiment(
    experimentId: string, 
    tenantId: string, 
    updateData: Partial<Experiment>
  ): Promise<Experiment | null> {
    try {
      const experimentIndex = this.experiments.findIndex(exp => 
        exp.id === experimentId && exp.tenantId === tenantId
      );

      if (experimentIndex === -1) {
        return null;
      }

      const experiment = this.experiments[experimentIndex];
      const updatedExperiment = {
        ...experiment,
        ...updateData,
        updatedAt: new Date()
      };

      this.experiments[experimentIndex] = updatedExperiment;

      this.logger.info('Experiment updated', {
        experimentId,
        tenantId,
        changes: Object.keys(updateData)
      });

      return updatedExperiment;
    } catch (error) {
      this.logger.error('Failed to update experiment', { error, experimentId, updateData });
      throw error;
    }
  }

  async deleteExperiment(experimentId: string, tenantId: string): Promise<boolean> {
    try {
      const experimentIndex = this.experiments.findIndex(exp => 
        exp.id === experimentId && exp.tenantId === tenantId
      );

      if (experimentIndex === -1) {
        return false;
      }

      this.experiments.splice(experimentIndex, 1);

      this.logger.info('Experiment deleted', { experimentId, tenantId });

      return true;
    } catch (error) {
      this.logger.error('Failed to delete experiment', { error, experimentId, tenantId });
      throw error;
    }
  }

  async publishExperiment(experimentId: string, tenantId: string, userId: string): Promise<Experiment | null> {
    try {
      const experiment = await this.updateExperiment(experimentId, tenantId, {
        status: 'active',
        updatedBy: userId
      });

      if (experiment) {
        this.logger.info('Experiment published', { experimentId, tenantId, userId });
      }

      return experiment;
    } catch (error) {
      this.logger.error('Failed to publish experiment', { error, experimentId, tenantId });
      throw error;
    }
  }

  async pauseExperiment(experimentId: string, tenantId: string, userId: string): Promise<Experiment | null> {
    try {
      const experiment = await this.updateExperiment(experimentId, tenantId, {
        status: 'paused',
        updatedBy: userId
      });

      if (experiment) {
        this.logger.info('Experiment paused', { experimentId, tenantId, userId });
      }

      return experiment;
    } catch (error) {
      this.logger.error('Failed to pause experiment', { error, experimentId, tenantId });
      throw error;
    }
  }

  async archiveExperiment(experimentId: string, tenantId: string, userId: string): Promise<Experiment | null> {
    try {
      const experiment = await this.updateExperiment(experimentId, tenantId, {
        status: 'archived',
        updatedBy: userId
      });

      if (experiment) {
        this.logger.info('Experiment archived', { experimentId, tenantId, userId });
      }

      return experiment;
    } catch (error) {
      this.logger.error('Failed to archive experiment', { error, experimentId, tenantId });
      throw error;
    }
  }

  async cloneExperiment(
    sourceId: string, 
    tenantId: string, 
    userId: string, 
    newName?: string
  ): Promise<Experiment | null> {
    try {
      const sourceExperiment = await this.getExperiment(sourceId, tenantId);
      
      if (!sourceExperiment) {
        return null;
      }

      const clonedExperiment = await this.createExperiment({
        name: newName || `${sourceExperiment.name} (Copy)`,
        description: sourceExperiment.description,
        type: sourceExperiment.type,
        tenantId,
        createdBy: userId
      });

      this.logger.info('Experiment cloned', {
        sourceId,
        clonedId: clonedExperiment.id,
        tenantId,
        userId
      });

      return clonedExperiment;
    } catch (error) {
      this.logger.error('Failed to clone experiment', { error, sourceId, tenantId });
      throw error;
    }
  }

  // Mock methods for other operations
  async getExperimentVariants(experimentId: string, tenantId: string): Promise<any[]> {
    return [
      { id: 'var_1', name: 'Control', traffic: 50 },
      { id: 'var_2', name: 'Variant A', traffic: 50 }
    ];
  }

  async createVariant(experimentId: string, tenantId: string, variantData: any): Promise<any> {
    return { id: `var_${Date.now()}`, ...variantData };
  }

  async updateVariant(experimentId: string, variantId: string, tenantId: string, updateData: any): Promise<any> {
    return { id: variantId, ...updateData };
  }

  async deleteVariant(experimentId: string, variantId: string, tenantId: string): Promise<boolean> {
    return true;
  }

  async getExperimentAnalytics(experimentId: string, tenantId: string, options: any): Promise<any> {
    return {
      conversions: 150,
      visitors: 1000,
      conversionRate: 0.15,
      variants: [
        { id: 'var_1', conversions: 75, visitors: 500, conversionRate: 0.15 },
        { id: 'var_2', conversions: 75, visitors: 500, conversionRate: 0.15 }
      ]
    };
  }

  async exportAnalytics(experimentId: string, tenantId: string, format: string): Promise<any> {
    return {
      url: `https://example.com/exports/${experimentId}_analytics.${format}`,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
    };
  }

  async getCollaborators(experimentId: string, tenantId: string): Promise<any[]> {
    return [
      { id: 'user_1', email: '<EMAIL>', role: 'editor' }
    ];
  }

  async addCollaborator(
    experimentId: string, 
    tenantId: string, 
    collaboratorId: string, 
    role: string, 
    addedBy: string
  ): Promise<void> {
    this.logger.info('Collaborator added', { experimentId, collaboratorId, role, addedBy });
  }

  async removeCollaborator(
    experimentId: string, 
    tenantId: string, 
    collaboratorId: string, 
    removedBy: string
  ): Promise<void> {
    this.logger.info('Collaborator removed', { experimentId, collaboratorId, removedBy });
  }

  async getAllExperiments(options: { page?: number; limit?: number }): Promise<any> {
    const page = options.page || 1;
    const limit = options.limit || 50;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;

    return {
      data: this.experiments.slice(startIndex, endIndex),
      total: this.experiments.length,
      page,
      limit
    };
  }

  async forceStopExperiment(experimentId: string, adminUserId: string, reason: string): Promise<Experiment | null> {
    const experiment = this.experiments.find(exp => exp.id === experimentId);
    
    if (experiment) {
      experiment.status = 'stopped';
      experiment.updatedBy = adminUserId;
      experiment.updatedAt = new Date();

      this.logger.warn('Experiment force stopped by admin', {
        experimentId,
        adminUserId,
        reason
      });

      return experiment;
    }

    return null;
  }

  async getExperimentAuditLog(experimentId: string, tenantId: string, options: any): Promise<any> {
    return {
      data: [
        {
          id: 'audit_1',
          action: 'created',
          userId: 'experimenter_456',
          timestamp: new Date(),
          details: { experimentName: 'Button Color Test' }
        }
      ],
      total: 1,
      page: options.page || 1,
      limit: options.limit || 50
    };
  }
}
