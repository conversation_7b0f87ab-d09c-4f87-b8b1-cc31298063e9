// Simple App component to test our data table
import React from 'react';

function App() {
  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-6">
          React Experiment Data Table Demo
        </h1>
        <p className="text-gray-600 mb-8">
          This is a simple test to verify our React setup is working.
        </p>

        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">✅ React App Running Successfully!</h2>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• Node.js: v24.2.0</p>
            <p>• React: 18.2.0</p>
            <p>• TypeScript: 5.3.2</p>
            <p>• Tailwind CSS: Working</p>
          </div>

          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <p className="text-blue-800 font-medium">
              🎉 Ready to integrate the ExperimentDataTable component!
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
