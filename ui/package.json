{"name": "ab-testing-dashboard", "version": "1.0.0", "description": "React dashboard for A/B testing experiment management", "private": true, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "ajv": "^8.17.1", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.5", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-datepicker": "^4.21.0", "react-dom": "^18.2.0", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.18.0", "react-scripts": "5.0.1", "react-select": "^5.8.0", "recharts": "^2.8.0", "tailwindcss": "^3.3.6", "typescript": "^5.3.2", "web-vitals": "^3.5.0", "zod": "^3.22.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@types/react-beautiful-dnd": "^13.1.7", "@types/react-datepicker": "^4.19.4", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-react-app": "^7.0.1", "postcss": "^8.4.31", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,md}", "type-check": "tsc --noEmit"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3001"}