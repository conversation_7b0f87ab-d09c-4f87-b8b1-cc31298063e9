const express = require('express');
const cors = require('cors');

console.log('🚀 Starting A/B Testing Platform Demo Server\n');

// Mock RBAC Service
class MockRBACService {
  constructor() {
    // Mock user roles
    this.userRoles = new Map([
      ['admin_123', ['admin']],
      ['experimenter_456', ['experimenter']],
      ['viewer_789', ['viewer']],
      ['user_999', ['experimenter']]
    ]);

    // Mock role permissions
    this.rolePermissions = new Map([
      ['viewer', ['experiments:read', 'analytics:view']],
      ['experimenter', [
        'experiments:read', 'experiments:create', 'experiments:update', 'experiments:publish',
        'analytics:view', 'analytics:export'
      ]],
      ['admin', [
        'experiments:read', 'experiments:create', 'experiments:update', 'experiments:delete',
        'experiments:publish', 'experiments:archive',
        'analytics:view', 'analytics:export',
        'users:read', 'users:create', 'users:update', 'users:delete', 'users:invite',
        'settings:read', 'settings:update',
        'roles:assign', 'roles:unassign'
      ]]
    ]);
  }

  async checkPermission(request) {
    const { userId, resource, action } = request;
    
    // Get user roles
    const userRoles = this.userRoles.get(userId) || [];
    
    // Get all permissions for user's roles
    const userPermissions = new Set();
    for (const role of userRoles) {
      const rolePerms = this.rolePermissions.get(role) || [];
      rolePerms.forEach(perm => userPermissions.add(perm));
    }

    // Check if user has the required permission
    const requiredPermission = `${resource}:${action}`;
    const hasPermission = userPermissions.has(requiredPermission);

    return {
      granted: hasPermission,
      reason: hasPermission 
        ? `Permission granted for ${action} on ${resource}`
        : `Missing permission: ${requiredPermission}`,
      matchedPermissions: hasPermission ? [{ resource, action, name: requiredPermission }] : [],
      appliedConditions: [],
      denyReasons: hasPermission ? [] : [`User does not have permission: ${requiredPermission}`]
    };
  }

  async getUserRoles(userId, tenantId) {
    const userRoleNames = this.userRoles.get(userId) || [];
    
    return userRoleNames.map(roleName => ({
      id: `role_${roleName}`,
      name: roleName,
      displayName: this.capitalizeFirst(roleName),
      description: `${this.capitalizeFirst(roleName)} role`,
      permissions: this.rolePermissions.get(roleName) || []
    }));
  }

  capitalizeFirst(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }
}

// Mock Experiment Service
class MockExperimentService {
  constructor() {
    this.experiments = [
      {
        id: 'exp_001',
        name: 'Button Color Test',
        description: 'Testing different button colors for conversion',
        type: 'ab_test',
        status: 'active',
        tenantId: 'tenant_123',
        createdBy: 'experimenter_456',
        createdAt: new Date('2023-12-01'),
        updatedAt: new Date('2023-12-01')
      },
      {
        id: 'exp_002',
        name: 'Pricing Page Layout',
        description: 'Testing different pricing page layouts',
        type: 'multivariate',
        status: 'draft',
        tenantId: 'tenant_123',
        createdBy: 'admin_123',
        createdAt: new Date('2023-12-02'),
        updatedAt: new Date('2023-12-02')
      },
      {
        id: 'exp_003',
        name: 'Checkout Flow Optimization',
        description: 'Optimizing the checkout flow for better conversion',
        type: 'ab_test',
        status: 'paused',
        tenantId: 'tenant_123',
        createdBy: 'experimenter_456',
        createdAt: new Date('2023-12-03'),
        updatedAt: new Date('2023-12-03')
      }
    ];
  }

  async getExperiment(experimentId, tenantId) {
    return this.experiments.find(exp => 
      exp.id === experimentId && exp.tenantId === tenantId
    ) || null;
  }

  async listExperiments(options) {
    let filteredExperiments = this.experiments.filter(exp => 
      exp.tenantId === options.tenantId
    );

    const total = filteredExperiments.length;
    const page = options.page || 1;
    const limit = options.limit || 20;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;

    const data = filteredExperiments.slice(startIndex, endIndex);

    return { data, total, page, limit };
  }

  async createExperiment(experimentData) {
    const newExperiment = {
      id: `exp_${Date.now()}`,
      name: experimentData.name || 'New Experiment',
      description: experimentData.description || '',
      type: experimentData.type || 'ab_test',
      status: 'draft',
      tenantId: experimentData.tenantId,
      createdBy: experimentData.createdBy,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.experiments.push(newExperiment);
    return newExperiment;
  }

  async updateExperiment(experimentId, tenantId, updateData) {
    const experimentIndex = this.experiments.findIndex(exp => 
      exp.id === experimentId && exp.tenantId === tenantId
    );

    if (experimentIndex === -1) {
      return null;
    }

    const experiment = this.experiments[experimentIndex];
    const updatedExperiment = {
      ...experiment,
      ...updateData,
      updatedAt: new Date()
    };

    this.experiments[experimentIndex] = updatedExperiment;
    return updatedExperiment;
  }

  async deleteExperiment(experimentId, tenantId) {
    const experimentIndex = this.experiments.findIndex(exp => 
      exp.id === experimentId && exp.tenantId === tenantId
    );

    if (experimentIndex === -1) {
      return false;
    }

    this.experiments.splice(experimentIndex, 1);
    return true;
  }

  async publishExperiment(experimentId, tenantId, userId) {
    const experiment = await this.updateExperiment(experimentId, tenantId, {
      status: 'active',
      updatedBy: userId
    });

    return experiment;
  }
}

// Authorization Middleware
function createAuthorizationMiddleware(rbacService, experimentService) {
  const authorize = (options) => {
    return async (req, res, next) => {
      try {
        const user = req.auth?.user;
        
        if (!user) {
          return res.status(401).json({
            success: false,
            error: 'Authentication required',
            code: 'AUTHENTICATION_REQUIRED'
          });
        }

        // Check RBAC permissions
        const permissionResult = await rbacService.checkPermission({
          userId: user.id,
          tenantId: user.tenantId,
          resource: options.resource,
          action: options.action
        });

        if (!permissionResult.granted) {
          return res.status(403).json({
            success: false,
            error: permissionResult.reason,
            code: 'INSUFFICIENT_PERMISSIONS',
            details: {
              requiredPermission: `${options.resource}:${options.action}`,
              denyReasons: permissionResult.denyReasons
            }
          });
        }

        // Check ownership if required
        if (options.requireOwnership && req.params.id) {
          const experiment = await experimentService.getExperiment(req.params.id, user.tenantId);
          
          if (!experiment) {
            return res.status(404).json({
              success: false,
              error: 'Experiment not found',
              code: 'EXPERIMENT_NOT_FOUND'
            });
          }

          const isOwner = experiment.createdBy === user.id;
          const userRoles = await rbacService.getUserRoles(user.id, user.tenantId);
          const isAdmin = userRoles.some(role => ['admin'].includes(role.name));

          if (!isOwner && !isAdmin) {
            return res.status(403).json({
              success: false,
              error: 'User does not own this experiment',
              code: 'NOT_EXPERIMENT_OWNER',
              details: {
                experimentId: req.params.id,
                experimentOwner: experiment.createdBy,
                requestingUser: user.id
              }
            });
          }
        }

        next();
      } catch (error) {
        console.error('Authorization middleware error:', error);
        return res.status(500).json({
          success: false,
          error: 'Authorization check failed',
          code: 'AUTHORIZATION_ERROR'
        });
      }
    };
  };

  return {
    authorize,

    authorizeExperiment: (action, requireOwnership = false) => {
      return authorize({
        resource: 'experiments',
        action,
        requireOwnership
      });
    },

    authorizeUserManagement: (action) => {
      return authorize({
        resource: 'users',
        action
      });
    },

    authorizeAnalytics: (action = 'view') => {
      return authorize({
        resource: 'analytics',
        action
      });
    },

    authorizeAdmin: (resource = 'settings', action = 'read') => {
      return authorize({
        resource,
        action
      });
    },

    authorizeMultiple: (permissions, requireAll = true) => {
      return async (req, res, next) => {
        const user = req.auth?.user;
        
        if (!user) {
          return res.status(401).json({
            success: false,
            error: 'Authentication required',
            code: 'AUTHENTICATION_REQUIRED'
          });
        }

        const results = [];
        for (const perm of permissions) {
          const result = await rbacService.checkPermission({
            userId: user.id,
            tenantId: user.tenantId,
            resource: perm.resource,
            action: perm.action
          });
          results.push({ ...perm, granted: result.granted });
        }

        const grantedCount = results.filter(r => r.granted).length;
        const hasAccess = requireAll ? grantedCount === permissions.length : grantedCount > 0;

        if (!hasAccess) {
          return res.status(403).json({
            success: false,
            error: `Access denied (${grantedCount}/${permissions.length} permissions)`,
            code: 'INSUFFICIENT_PERMISSIONS',
            details: {
              requiredPermissions: permissions,
              grantedCount,
              requireAll,
              results
            }
          });
        }

        next();
      };
    }
  };
}

// Create Express app
const app = express();

// Initialize services
const rbacService = new MockRBACService();
const experimentService = new MockExperimentService();
const authMiddleware = createAuthorizationMiddleware(rbacService, experimentService);

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3003'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Request-ID']
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Serve static files from public directory
app.use(express.static('public'));

// Request ID middleware
app.use((req, res, next) => {
  req.headers['x-request-id'] = req.headers['x-request-id'] || 
    `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  next();
});

// Request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} [${req.method}] ${req.url} - ${req.ip}`);
  next();
});

// Health check endpoint (no auth required) - MUST be before auth middleware
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'A/B Testing Platform is healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    features: [
      'Experiment Management',
      'Authorization Middleware',
      'Role-Based Access Control',
      'Multi-Tenant Support',
      'Audit Logging'
    ]
  });
});

// Mock authentication middleware (applies to all routes AFTER this point)
app.use((req, res, next) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader) {
    return res.status(401).json({
      success: false,
      error: 'Authorization header required',
      code: 'MISSING_AUTH_HEADER',
      requestId: req.headers['x-request-id']
    });
  }

  // Mock user based on token
  const token = authHeader.replace('Bearer ', '');
  let mockUser;

  switch (token) {
    case 'admin_token':
      mockUser = {
        id: 'admin_123',
        email: '<EMAIL>',
        tenantId: 'tenant_123',
        role: 'admin'
      };
      break;
    case 'experimenter_token':
      mockUser = {
        id: 'experimenter_456',
        email: '<EMAIL>',
        tenantId: 'tenant_123',
        role: 'experimenter'
      };
      break;
    case 'viewer_token':
      mockUser = {
        id: 'viewer_789',
        email: '<EMAIL>',
        tenantId: 'tenant_123',
        role: 'viewer'
      };
      break;
    case 'other_tenant_token':
      mockUser = {
        id: 'user_999',
        email: '<EMAIL>',
        tenantId: 'tenant_999',
        role: 'experimenter'
      };
      break;
    default:
      return res.status(401).json({
        success: false,
        error: 'Invalid token',
        code: 'INVALID_TOKEN',
        requestId: req.headers['x-request-id']
      });
  }

  req.auth = { user: mockUser };
  next();
});

// API Routes
app.get('/api/experiments',
  authMiddleware.authorizeExperiment('read'),
  async (req, res) => {
    try {
      const tenantId = req.auth?.user?.tenantId;
      const experiments = await experimentService.listExperiments({
        tenantId: tenantId,
        page: 1,
        limit: 20
      });

      res.json({
        success: true,
        data: experiments
      });
    } catch (error) {
      console.error('List experiments failed:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to list experiments'
      });
    }
  }
);

app.get('/api/experiments/:id',
  authMiddleware.authorizeExperiment('read', true), // Require ownership
  async (req, res) => {
    try {
      const { id } = req.params;
      const tenantId = req.auth?.user?.tenantId;
      const experiment = await experimentService.getExperiment(id, tenantId);

      if (!experiment) {
        return res.status(404).json({
          success: false,
          error: 'Experiment not found',
          code: 'EXPERIMENT_NOT_FOUND'
        });
      }

      res.json({
        success: true,
        data: { experiment }
      });
    } catch (error) {
      console.error('Get experiment failed:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get experiment'
      });
    }
  }
);

app.post('/api/experiments',
  authMiddleware.authorizeExperiment('create'),
  async (req, res) => {
    try {
      const tenantId = req.auth?.user?.tenantId;
      const userId = req.auth?.user?.id;
      const experiment = await experimentService.createExperiment({
        ...req.body,
        tenantId,
        createdBy: userId
      });

      res.status(201).json({
        success: true,
        data: { experiment }
      });
    } catch (error) {
      console.error('Create experiment failed:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create experiment'
      });
    }
  }
);

// User management routes
app.get('/api/users',
  authMiddleware.authorizeUserManagement('read'),
  (req, res) => {
    res.json({
      success: true,
      data: {
        users: [
          { id: 'admin_123', email: '<EMAIL>', role: 'admin' },
          { id: 'experimenter_456', email: '<EMAIL>', role: 'experimenter' },
          { id: 'viewer_789', email: '<EMAIL>', role: 'viewer' }
        ]
      }
    });
  }
);

// Analytics routes
app.get('/api/analytics/dashboard',
  authMiddleware.authorizeAnalytics('view'),
  (req, res) => {
    res.json({
      success: true,
      data: {
        totalExperiments: 25,
        activeExperiments: 8,
        totalConversions: 1250,
        conversionRate: 0.15,
        recentExperiments: [
          { id: 'exp_001', name: 'Button Color Test', status: 'active', conversionRate: 0.18 },
          { id: 'exp_002', name: 'Pricing Page Layout', status: 'draft', conversionRate: null },
          { id: 'exp_003', name: 'Checkout Flow', status: 'paused', conversionRate: 0.12 }
        ]
      }
    });
  }
);

app.get('/api/analytics/export',
  authMiddleware.authorizeAnalytics('export'),
  (req, res) => {
    res.json({
      success: true,
      data: {
        exportUrl: 'https://example.com/exports/analytics_2023.csv',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        format: 'csv',
        size: '2.5MB'
      }
    });
  }
);

// Admin routes
app.get('/api/admin/settings',
  authMiddleware.authorizeAdmin('settings', 'read'),
  (req, res) => {
    res.json({
      success: true,
      data: {
        tenantName: 'Example Corp',
        maxExperiments: 100,
        retentionDays: 90,
        features: ['advanced_analytics', 'custom_events', 'api_access'],
        billing: {
          plan: 'enterprise',
          usage: {
            experiments: 25,
            events: 1250000
          }
        }
      }
    });
  }
);

// Error handling
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    code: 'INTERNAL_ERROR',
    requestId: req.headers['x-request-id']
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found',
    code: 'NOT_FOUND',
    requestId: req.headers['x-request-id']
  });
});

// Start server
const PORT = process.env.PORT || 3003;

app.listen(PORT, () => {
  console.log(`🚀 A/B Testing Platform running on port ${PORT}`);
  
  console.log('\n📚 API Endpoints:');
  console.log('   GET  /health                     - Health check (no auth required)');
  console.log('   GET  /api/experiments            - List experiments');
  console.log('   GET  /api/experiments/:id        - Get experiment (requires ownership)');
  console.log('   POST /api/experiments            - Create experiment');
  console.log('   GET  /api/users                  - List users (admin only)');
  console.log('   GET  /api/analytics/dashboard    - View analytics dashboard');
  console.log('   GET  /api/analytics/export       - Export analytics data');
  console.log('   GET  /api/admin/settings         - Admin settings');
  
  console.log('\n🔑 Test Tokens:');
  console.log('   admin_token           - Admin user (full access)');
  console.log('   experimenter_token    - Experimenter user (experiment management)');
  console.log('   viewer_token          - Viewer user (read-only access)');
  console.log('   other_tenant_token    - User from different tenant');
  
  console.log('\n🧪 Example Requests:');
  console.log('   curl http://localhost:3003/health');
  console.log('   curl -H "Authorization: Bearer admin_token" http://localhost:3003/api/experiments');
  console.log('   curl -H "Authorization: Bearer viewer_token" http://localhost:3003/api/users');
  console.log('   curl -X POST -H "Authorization: Bearer experimenter_token" \\');
  console.log('        -H "Content-Type: application/json" \\');
  console.log('        -d \'{"name":"My Test","description":"Testing the platform"}\' \\');
  console.log('        http://localhost:3003/api/experiments');
  
  console.log(`\n🏥 Health Check: http://localhost:${PORT}/health`);
  console.log('✨ A/B Testing Platform is ready for testing!');
});
